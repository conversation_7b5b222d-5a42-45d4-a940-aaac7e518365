
    <!DOCTYPE html>
    <html>
    <head>
        <title>Krayin CRM Comprehensive Report</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f6fa;
                color: #2c3e50;
            }
            .container { max-width: 1200px; margin: 0 auto; }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 10px;
                margin-bottom: 30px;
                text-align: center;
            }
            h1 { margin: 0; font-size: 2.5em; font-weight: 300; }
            .report-date {
                color: rgba(255,255,255,0.8);
                font-size: 16px;
                margin-top: 10px;
            }
            .dashboard {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .metric-card {
                background: white;
                padding: 25px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                text-align: center;
                transition: transform 0.2s;
            }
            .metric-card:hover { transform: translateY(-2px); }
            .metric-number {
                font-size: 2.5em;
                font-weight: bold;
                color: #667eea;
                margin-bottom: 5px;
            }
            .metric-label {
                color: #7f8c8d;
                font-size: 14px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            .section {
                background: white;
                padding: 25px;
                border-radius: 10px;
                margin-bottom: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .section h2 {
                color: #2c3e50;
                border-bottom: 3px solid #667eea;
                padding-bottom: 10px;
                margin-bottom: 20px;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin-top: 15px;
                background: white;
            }
            th {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 12px;
                text-align: left;
                font-weight: 500;
            }
            td {
                padding: 12px;
                border-bottom: 1px solid #ecf0f1;
            }
            tr:hover { background-color: #f8f9fa; }
            .status-badge {
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: bold;
                text-transform: uppercase;
            }
            .status-new { background-color: #e3f2fd; color: #1976d2; }
            .status-qualified { background-color: #f3e5f5; color: #7b1fa2; }
            .status-proposal { background-color: #fff3e0; color: #f57c00; }
            .status-won { background-color: #e8f5e8; color: #388e3c; }
            .status-lost { background-color: #ffebee; color: #d32f2f; }
            .grid-2 {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }
            @media (max-width: 768px) {
                .grid-2 { grid-template-columns: 1fr; }
                .dashboard { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Krayin CRM Comprehensive Report</h1>
                <div class="report-date">Generated on: 2025-06-19 11:55:54</div>
            </div>

            <div class="dashboard">
                <div class="metric-card">
                    <div class="metric-number">19</div>
                    <div class="metric-label">Total Customers</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">0</div>
                    <div class="metric-label">Total Leads</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">0</div>
                    <div class="metric-label">Total Quotes</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">0</div>
                    <div class="metric-label">Products</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">1</div>
                    <div class="metric-label">Active Users</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">0</div>
                    <div class="metric-label">Organizations</div>
                </div>
            </div>

            <div class="grid-2">
                <div class="section">
                    <h2>📊 Leads by Status</h2>
                    <table border="1" class="dataframe table">
  <thead>
    <tr style="text-align: right;">
      <th>status</th>
      <th>count</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>No Data</td>
      <td>0</td>
    </tr>
  </tbody>
</table>
                </div>

                <div class="section">
                    <h2>🚀 Pipeline Overview</h2>
                    <table border="1" class="dataframe table">
  <thead>
    <tr style="text-align: right;">
      <th>pipeline_name</th>
      <th>leads_count</th>
      <th>total_value</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Default Pipeline</td>
      <td>15</td>
      <td>362363.6700</td>
    </tr>
  </tbody>
</table>
                </div>
            </div>

            <div class="section">
                <h2>💰 Financial Summary</h2>
                <div class="dashboard">
                    <div class="metric-card">
                        <div class="metric-number" style="font-size: 1.8em;">$0.00</div>
                        <div class="metric-label">Total Quote Value</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" style="font-size: 1.8em;">$0.00</div>
                        <div class="metric-label">Average Quote Value</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" style="font-size: 1.8em;">$0.00</div>
                        <div class="metric-label">Inventory Value</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📈 Recent Activities (Last 30 Days)</h2>
                <p>No recent activities found</p>
            </div>

            <div class="section">
                <h2>📋 Report Summary</h2>
                <ul style="line-height: 1.8;">
                    <li><strong>Total Customers:</strong> 19 active customers in the system</li>
                    <li><strong>Lead Management:</strong> 0 leads across all pipelines</li>
                    <li><strong>Sales Activity:</strong> 0 quotes generated with $0.00 total value</li>
                    <li><strong>Product Catalog:</strong> 0 products with $0.00 inventory value</li>
                    <li><strong>Team:</strong> 1 active users managing 0 organizations</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px;">
                <p>This report was automatically generated by Krayin CRM Reporting System</p>
            </div>
        </div>
    </body>
    </html>
    