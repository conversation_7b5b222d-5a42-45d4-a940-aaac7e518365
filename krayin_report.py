import mysql.connector
import pandas as pd
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime
import sys

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root@123',
    'database': 'laravel-crm',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_general_ci',
    'autocommit': True
}

# Email configuration
EMAIL_CONFIG = {
    'sender': '<EMAIL>',
    'recipients': ['<EMAIL>'],
    'smtp_server': 'smtp.gmail.com',
    'smtp_port': 587,
    'username': '<EMAIL>',
    'password': 'Aa@9931893764'  # Replace with Gmail App Password (16 characters)
}

def connect_to_db():
    """Connect to the MariaDB database"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        print("Database connection successful!")
        return conn
    except mysql.connector.Error as err:
        print(f"Database connection error: {err}")
        print("Trying alternative connection without collation...")
        try:
            # Try without collation setting
            config_alt = DB_CONFIG.copy()
            config_alt.pop('collation', None)
            conn = mysql.connector.connect(**config_alt)
            print("Database connection successful with alternative config!")
            return conn
        except mysql.connector.Error as err2:
            print(f"Alternative connection also failed: {err2}")
            return None

def get_crm_data(conn):
    """Fetch relevant data from Krayin CRM database"""
    cursor = conn.cursor(dictionary=True)

    try:
        # First, let's check what tables exist
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print("Available tables:", [list(table.values())[0] for table in tables])

        # Initialize default data
        customer_data = {'total_customers': 0}
        leads_data = []

        # Try to get customer data
        try:
            customer_query = """
            SELECT COUNT(*) as total_customers FROM customers
            """
            cursor.execute(customer_query)
            customer_data = cursor.fetchone()
            print("Customer data retrieved successfully")
        except mysql.connector.Error as e:
            print(f"Error fetching customer data: {e}")
            # Try alternative table names
            try:
                cursor.execute("SELECT COUNT(*) as total_customers FROM persons")
                customer_data = cursor.fetchone()
                print("Customer data retrieved from 'persons' table")
            except mysql.connector.Error:
                print("Using default customer data")

        # Try to get leads data
        try:
            leads_query = """
            SELECT status, COUNT(*) as count
            FROM leads
            GROUP BY status
            """
            cursor.execute(leads_query)
            leads_data = cursor.fetchall()
            print("Leads data retrieved successfully")
        except mysql.connector.Error as e:
            print(f"Error fetching leads data: {e}")
            # Provide default data
            leads_data = [{'status': 'No Data', 'count': 0}]

        cursor.close()

        return {
            'customer_data': customer_data,
            'leads_data': leads_data
        }

    except Exception as e:
        print(f"Error in get_crm_data: {e}")
        cursor.close()
        return {
            'customer_data': {'total_customers': 0},
            'leads_data': [{'status': 'Error', 'count': 0}]
        }

def generate_html_report(data):
    """Generate HTML report from the data"""
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Convert leads data to DataFrame for easier HTML generation
    leads_df = pd.DataFrame(data['leads_data'])
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Krayin CRM Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1 {{ color: #2c3e50; }}
            .report-date {{ color: #7f8c8d; font-size: 14px; margin-bottom: 20px; }}
            .summary-box {{ background-color: #f8f9fa; border-radius: 5px; padding: 15px; margin-bottom: 20px; }}
            table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
        </style>
    </head>
    <body>
        <h1>Krayin CRM Report</h1>
        <div class="report-date">Generated on: {now}</div>
        
        <div class="summary-box">
            <h2>Summary</h2>
            <p>Total Customers: {data['customer_data']['total_customers']}</p>
        </div>
        
        <h2>Leads by Status</h2>
        {leads_df.to_html(index=False)}
        
        <!-- Add more sections as needed -->
        
    </body>
    </html>
    """
    
    return html

def test_email_connection():
    """Test email connection without sending a full report"""
    try:
        server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'])
        server.starttls()
        server.login(EMAIL_CONFIG['username'], EMAIL_CONFIG['password'])
        server.quit()
        print("✅ Email connection test successful!")
        return True
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ Email authentication failed: {e}")
        print("Please check your email credentials and use an App Password for Gmail.")
        return False
    except Exception as e:
        print(f"❌ Email connection failed: {e}")
        return False

def send_email(html_content):
    """Send the HTML report via email"""
    try:
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['sender']
        msg['To'] = ", ".join(EMAIL_CONFIG['recipients'])
        msg['Subject'] = f"Krayin CRM Report - {datetime.now().strftime('%Y-%m-%d')}"

        msg.attach(MIMEText(html_content, 'html'))

        server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'])
        server.starttls()
        server.login(EMAIL_CONFIG['username'], EMAIL_CONFIG['password'])
        server.send_message(msg)
        server.quit()

        print("Email sent successfully!")
    except smtplib.SMTPAuthenticationError as e:
        print(f"SMTP Authentication failed: {e}")
        print("Note: For Gmail, you need to:")
        print("1. Enable 2-factor authentication on your Google account")
        print("2. Generate an App Password for this application")
        print("3. Use the App Password instead of your regular password")
        print("4. Visit: https://support.google.com/accounts/answer/185833")
    except Exception as e:
        print(f"Failed to send email: {e}")
        print("Email sending failed, but the report was generated successfully.")

def main():
    print("Starting Krayin CRM Report Generation...")

    # Connect to database
    conn = connect_to_db()
    if not conn:
        print("Failed to connect to database. Exiting.")
        return

    try:
        # Get data
        print("Fetching CRM data...")
        data = get_crm_data(conn)

        # Generate HTML report
        print("Generating HTML report...")
        html_report = generate_html_report(data)

        # Save report to file
        report_filename = f"krayin_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        with open(report_filename, "w", encoding='utf-8') as f:
            f.write(html_report)
        print(f"Report saved to: {report_filename}")

        # Send email
        print("Attempting to send email...")
        send_email(html_report)

        print("Report generation completed!")

    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        conn.close()
        print("Database connection closed.")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--test-email":
        print("Testing email configuration...")
        test_email_connection()
    else:
        main()