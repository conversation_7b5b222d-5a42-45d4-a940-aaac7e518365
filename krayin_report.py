import mysql.connector
import pandas as pd
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime
import sys

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root@123',
    'database': 'laravel-crm',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_general_ci',
    'autocommit': True
}

# Email configuration
EMAIL_CONFIG = {
    'sender': '<EMAIL>',
    'recipients': ['<EMAIL>'],
    'smtp_server': 'smtp.gmail.com',
    'smtp_port': 587,
    'username': '<EMAIL>',
    'password': 'apif hcdh fewr ldrg' 
}

def connect_to_db():
    """Connect to the MariaDB database"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        print("Database connection successful!")
        return conn
    except mysql.connector.Error as err:
        print(f"Database connection error: {err}")
        print("Trying alternative connection without collation...")
        try:
            # Try without collation setting
            config_alt = DB_CONFIG.copy()
            config_alt.pop('collation', None)
            conn = mysql.connector.connect(**config_alt)
            print("Database connection successful with alternative config!")
            return conn
        except mysql.connector.Error as err2:
            print(f"Alternative connection also failed: {err2}")
            return None

def get_crm_data(conn):
    """Fetch comprehensive data from Krayin CRM database"""
    cursor = conn.cursor(dictionary=True)

    try:
        # First, let's check what tables exist
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print("Available tables:", [list(table.values())[0] for table in tables])

        # Initialize comprehensive data structure
        data = {
            'customer_data': {'total_customers': 0},
            'leads_data': [],
            'pipeline_data': [],
            'activities_data': [],
            'quotes_data': [],
            'products_data': [],
            'users_data': [],
            'organizations_data': [],
            'revenue_data': {'total_revenue': 0, 'monthly_revenue': []},
            'summary_stats': {}
        }

        # Get customer/person data
        try:
            cursor.execute("SELECT COUNT(*) as total_customers FROM persons")
            data['customer_data'] = cursor.fetchone()
            print("Customer data retrieved from 'persons' table")
        except mysql.connector.Error as e:
            print(f"Error fetching customer data: {e}")

        # Get leads data with detailed breakdown
        try:
            # First try to get the correct column names
            cursor.execute("DESCRIBE leads")
            lead_columns = [col['Field'] for col in cursor.fetchall()]
            print(f"Lead table columns: {lead_columns}")

            # Check if we have the right columns
            if 'lead_pipeline_stage_id' in lead_columns:
                stage_column = 'lead_pipeline_stage_id'
            elif 'stage_id' in lead_columns:
                stage_column = 'stage_id'
            else:
                stage_column = 'id'  # fallback

            leads_query = f"""
            SELECT
                ls.name as status_name,
                COUNT(l.id) as count,
                AVG(l.lead_value) as avg_value,
                SUM(l.lead_value) as total_value
            FROM leads l
            LEFT JOIN lead_pipeline_stages ls ON l.{stage_column} = ls.id
            GROUP BY l.{stage_column}, ls.name
            ORDER BY COUNT(l.id) DESC
            """
            cursor.execute(leads_query)
            data['leads_data'] = cursor.fetchall()
            print("Detailed leads data retrieved successfully")
        except mysql.connector.Error as e:
            print(f"Error fetching detailed leads data: {e}")
            # Fallback to simple query
            try:
                cursor.execute("SELECT lead_pipeline_stage_id as status, COUNT(*) as count, SUM(lead_value) as total_value FROM leads GROUP BY lead_pipeline_stage_id")
                data['leads_data'] = cursor.fetchall()
                print("Basic leads data retrieved")
            except mysql.connector.Error as e2:
                print(f"Fallback query also failed: {e2}")
                data['leads_data'] = [{'status': 'No Data', 'count': 0}]

        # Get pipeline data
        try:
            pipeline_query = """
            SELECT
                lp.name as pipeline_name,
                COUNT(l.id) as leads_count,
                SUM(l.lead_value) as total_value
            FROM lead_pipelines lp
            LEFT JOIN leads l ON l.lead_pipeline_id = lp.id
            GROUP BY lp.id, lp.name
            """
            cursor.execute(pipeline_query)
            data['pipeline_data'] = cursor.fetchall()
            print("Pipeline data retrieved successfully")
        except mysql.connector.Error as e:
            print(f"Error fetching pipeline data: {e}")

        # Get recent activities
        try:
            activities_query = """
            SELECT
                type,
                COUNT(*) as count,
                DATE(created_at) as activity_date
            FROM activities
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY type, DATE(created_at)
            ORDER BY activity_date DESC
            LIMIT 10
            """
            cursor.execute(activities_query)
            data['activities_data'] = cursor.fetchall()
            print("Activities data retrieved successfully")
        except mysql.connector.Error as e:
            print(f"Error fetching activities data: {e}")

        # Get quotes data
        try:
            quotes_query = """
            SELECT
                COUNT(*) as total_quotes,
                SUM(grand_total) as total_quote_value,
                AVG(grand_total) as avg_quote_value
            FROM quotes
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            """
            cursor.execute(quotes_query)
            quote_result = cursor.fetchone()
            data['quotes_data'] = quote_result if quote_result else {'total_quotes': 0, 'total_quote_value': 0, 'avg_quote_value': 0}
            print("Quotes data retrieved successfully")
        except mysql.connector.Error as e:
            print(f"Error fetching quotes data: {e}")

        # Get products data
        try:
            products_query = """
            SELECT
                COUNT(*) as total_products,
                SUM(price) as total_inventory_value
            FROM products
            """
            cursor.execute(products_query)
            product_result = cursor.fetchone()
            data['products_data'] = product_result if product_result else {'total_products': 0, 'total_inventory_value': 0}
            print("Products data retrieved successfully")
        except mysql.connector.Error as e:
            print(f"Error fetching products data: {e}")

        # Get users data
        try:
            cursor.execute("SELECT COUNT(*) as total_users FROM users")
            user_result = cursor.fetchone()
            data['users_data'] = user_result if user_result else {'total_users': 0}
            print("Users data retrieved successfully")
        except mysql.connector.Error as e:
            print(f"Error fetching users data: {e}")

        # Get organizations data
        try:
            cursor.execute("SELECT COUNT(*) as total_organizations FROM organizations")
            org_result = cursor.fetchone()
            data['organizations_data'] = org_result if org_result else {'total_organizations': 0}
            print("Organizations data retrieved successfully")
        except mysql.connector.Error as e:
            print(f"Error fetching organizations data: {e}")

        cursor.close()
        return data

    except Exception as e:
        print(f"Error in get_crm_data: {e}")
        cursor.close()
        return {
            'customer_data': {'total_customers': 0},
            'leads_data': [{'status': 'Error', 'count': 0}],
            'pipeline_data': [],
            'activities_data': [],
            'quotes_data': {'total_quotes': 0, 'total_quote_value': 0, 'avg_quote_value': 0},
            'products_data': {'total_products': 0, 'total_inventory_value': 0},
            'users_data': {'total_users': 0},
            'organizations_data': {'total_organizations': 0},
            'revenue_data': {'total_revenue': 0, 'monthly_revenue': []},
            'summary_stats': {}
        }

def generate_html_report(data):
    """Generate comprehensive HTML report from the data"""
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Helper function to safely convert to DataFrame
    def safe_df(data_list, default_columns=None):
        if not data_list:
            return pd.DataFrame(columns=default_columns or ['No Data'])
        return pd.DataFrame(data_list)

    # Convert data to DataFrames
    leads_df = safe_df(data['leads_data'], ['Status', 'Count', 'Avg Value', 'Total Value'])
    pipeline_df = safe_df(data['pipeline_data'], ['Pipeline', 'Leads Count', 'Total Value'])
    activities_df = safe_df(data['activities_data'], ['Type', 'Count', 'Date'])

    # Calculate summary statistics
    total_customers = data['customer_data'].get('total_customers', 0)
    total_leads = sum([item.get('count', 0) for item in data['leads_data']])
    total_quotes = data['quotes_data'].get('total_quotes', 0)
    total_products = data['products_data'].get('total_products', 0)
    total_users = data['users_data'].get('total_users', 0)
    total_organizations = data['organizations_data'].get('total_organizations', 0)

    # Format currency values
    def format_currency(value):
        if value is None:
            return "$0.00"
        return f"${float(value):,.2f}"

    total_quote_value = format_currency(data['quotes_data'].get('total_quote_value', 0))
    avg_quote_value = format_currency(data['quotes_data'].get('avg_quote_value', 0))
    total_inventory_value = format_currency(data['products_data'].get('total_inventory_value', 0))

    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Krayin CRM Comprehensive Report</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f6fa;
                color: #2c3e50;
            }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 10px;
                margin-bottom: 30px;
                text-align: center;
            }}
            h1 {{ margin: 0; font-size: 2.5em; font-weight: 300; }}
            .report-date {{
                color: rgba(255,255,255,0.8);
                font-size: 16px;
                margin-top: 10px;
            }}
            .dashboard {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }}
            .metric-card {{
                background: white;
                padding: 25px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                text-align: center;
                transition: transform 0.2s;
            }}
            .metric-card:hover {{ transform: translateY(-2px); }}
            .metric-number {{
                font-size: 2.5em;
                font-weight: bold;
                color: #667eea;
                margin-bottom: 5px;
            }}
            .metric-label {{
                color: #7f8c8d;
                font-size: 14px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            .section {{
                background: white;
                padding: 25px;
                border-radius: 10px;
                margin-bottom: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .section h2 {{
                color: #2c3e50;
                border-bottom: 3px solid #667eea;
                padding-bottom: 10px;
                margin-bottom: 20px;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin-top: 15px;
                background: white;
            }}
            th {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 12px;
                text-align: left;
                font-weight: 500;
            }}
            td {{
                padding: 12px;
                border-bottom: 1px solid #ecf0f1;
            }}
            tr:hover {{ background-color: #f8f9fa; }}
            .status-badge {{
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: bold;
                text-transform: uppercase;
            }}
            .status-new {{ background-color: #e3f2fd; color: #1976d2; }}
            .status-qualified {{ background-color: #f3e5f5; color: #7b1fa2; }}
            .status-proposal {{ background-color: #fff3e0; color: #f57c00; }}
            .status-won {{ background-color: #e8f5e8; color: #388e3c; }}
            .status-lost {{ background-color: #ffebee; color: #d32f2f; }}
            .grid-2 {{
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }}
            @media (max-width: 768px) {{
                .grid-2 {{ grid-template-columns: 1fr; }}
                .dashboard {{ grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }}
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Krayin CRM Comprehensive Report</h1>
                <div class="report-date">Generated on: {now}</div>
            </div>

            <div class="dashboard">
                <div class="metric-card">
                    <div class="metric-number">{total_customers}</div>
                    <div class="metric-label">Total Customers</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">{total_leads}</div>
                    <div class="metric-label">Total Leads</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">{total_quotes}</div>
                    <div class="metric-label">Total Quotes</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">{total_products}</div>
                    <div class="metric-label">Products</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">{total_users}</div>
                    <div class="metric-label">Active Users</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">{total_organizations}</div>
                    <div class="metric-label">Organizations</div>
                </div>
            </div>

            <div class="grid-2">
                <div class="section">
                    <h2>📊 Leads by Status</h2>
                    {leads_df.to_html(index=False, classes='table', escape=False) if not leads_df.empty else '<p>No leads data available</p>'}
                </div>

                <div class="section">
                    <h2>🚀 Pipeline Overview</h2>
                    {pipeline_df.to_html(index=False, classes='table', escape=False) if not pipeline_df.empty else '<p>No pipeline data available</p>'}
                </div>
            </div>

            <div class="section">
                <h2>💰 Financial Summary</h2>
                <div class="dashboard">
                    <div class="metric-card">
                        <div class="metric-number" style="font-size: 1.8em;">{total_quote_value}</div>
                        <div class="metric-label">Total Quote Value</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" style="font-size: 1.8em;">{avg_quote_value}</div>
                        <div class="metric-label">Average Quote Value</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" style="font-size: 1.8em;">{total_inventory_value}</div>
                        <div class="metric-label">Inventory Value</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📈 Recent Activities (Last 30 Days)</h2>
                {activities_df.to_html(index=False, classes='table', escape=False) if not activities_df.empty else '<p>No recent activities found</p>'}
            </div>

            <div class="section">
                <h2>📋 Report Summary</h2>
                <ul style="line-height: 1.8;">
                    <li><strong>Total Customers:</strong> {total_customers} active customers in the system</li>
                    <li><strong>Lead Management:</strong> {total_leads} leads across all pipelines</li>
                    <li><strong>Sales Activity:</strong> {total_quotes} quotes generated with {total_quote_value} total value</li>
                    <li><strong>Product Catalog:</strong> {total_products} products with {total_inventory_value} inventory value</li>
                    <li><strong>Team:</strong> {total_users} active users managing {total_organizations} organizations</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px;">
                <p>This report was automatically generated by Krayin CRM Reporting System</p>
            </div>
        </div>
    </body>
    </html>
    """

    return html

def test_email_connection():
    """Test email connection without sending a full report"""
    try:
        server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'])
        server.starttls()
        server.login(EMAIL_CONFIG['username'], EMAIL_CONFIG['password'])
        server.quit()
        print("✅ Email connection test successful!")
        return True
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ Email authentication failed: {e}")
        print("Please check your email credentials and use an App Password for Gmail.")
        return False
    except Exception as e:
        print(f"❌ Email connection failed: {e}")
        return False

def send_email(html_content):
    """Send the HTML report via email"""
    try:
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['sender']
        msg['To'] = ", ".join(EMAIL_CONFIG['recipients'])
        msg['Subject'] = f"Krayin CRM Report - {datetime.now().strftime('%Y-%m-%d')}"

        msg.attach(MIMEText(html_content, 'html'))

        server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'])
        server.starttls()
        server.login(EMAIL_CONFIG['username'], EMAIL_CONFIG['password'])
        server.send_message(msg)
        server.quit()

        print("Email sent successfully!")
    except smtplib.SMTPAuthenticationError as e:
        print(f"SMTP Authentication failed: {e}")
        print("Note: For Gmail, you need to:")
        print("1. Enable 2-factor authentication on your Google account")
        print("2. Generate an App Password for this application")
        print("3. Use the App Password instead of your regular password")
        print("4. Visit: https://support.google.com/accounts/answer/185833")
    except Exception as e:
        print(f"Failed to send email: {e}")
        print("Email sending failed, but the report was generated successfully.")

def main():
    print("Starting Krayin CRM Report Generation...")

    # Connect to database
    conn = connect_to_db()
    if not conn:
        print("Failed to connect to database. Exiting.")
        return

    try:
        # Get data
        print("Fetching CRM data...")
        data = get_crm_data(conn)

        # Generate HTML report
        print("Generating HTML report...")
        html_report = generate_html_report(data)

        # Save report to file
        report_filename = f"krayin_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        with open(report_filename, "w", encoding='utf-8') as f:
            f.write(html_report)
        print(f"Report saved to: {report_filename}")

        # Send email
        print("Attempting to send email...")
        send_email(html_report)

        print("Report generation completed!")

    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        conn.close()
        print("Database connection closed.")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--test-email":
        print("Testing email configuration...")
        test_email_connection()
    else:
        main()