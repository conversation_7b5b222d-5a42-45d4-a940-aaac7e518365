import mysql.connector
import pandas as pd
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime

# Database connection parameters
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'root@123',
    'database': 'laravel-crm'
}

# Email configuration
EMAIL_CONFIG = {
    'sender': '<EMAIL>',
    'recipients': ['<EMAIL>'],
    'smtp_server': 'smtp.gmail.com',
    'smtp_port': 587,
    'username': '<EMAIL>',
    'password': 'Aa@9931893764'
}

def connect_to_db():
    """Connect to the MariaDB database"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except mysql.connector.Error as err:
        print(f"Database connection error: {err}")
        return None

def get_crm_data(conn):
    """Fetch relevant data from Krayin CRM database"""
    cursor = conn.cursor(dictionary=True)
    
    # Example queries - adjust based on your Krayin CRM schema
    customer_query = """
    SELECT COUNT(*) as total_customers FROM customers
    """
    
    leads_query = """
    SELECT status, COUNT(*) as count 
    FROM leads 
    GROUP BY status
    """
    
    # Add more queries as needed
    
    # Execute queries
    cursor.execute(customer_query)
    customer_data = cursor.fetchone()
    
    cursor.execute(leads_query)
    leads_data = cursor.fetchall()
    
    cursor.close()
    
    return {
        'customer_data': customer_data,
        'leads_data': leads_data
    }

def generate_html_report(data):
    """Generate HTML report from the data"""
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Convert leads data to DataFrame for easier HTML generation
    leads_df = pd.DataFrame(data['leads_data'])
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Krayin CRM Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1 {{ color: #2c3e50; }}
            .report-date {{ color: #7f8c8d; font-size: 14px; margin-bottom: 20px; }}
            .summary-box {{ background-color: #f8f9fa; border-radius: 5px; padding: 15px; margin-bottom: 20px; }}
            table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
        </style>
    </head>
    <body>
        <h1>Krayin CRM Report</h1>
        <div class="report-date">Generated on: {now}</div>
        
        <div class="summary-box">
            <h2>Summary</h2>
            <p>Total Customers: {data['customer_data']['total_customers']}</p>
        </div>
        
        <h2>Leads by Status</h2>
        {leads_df.to_html(index=False)}
        
        <!-- Add more sections as needed -->
        
    </body>
    </html>
    """
    
    return html

def send_email(html_content):
    """Send the HTML report via email"""
    try:
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['sender']
        msg['To'] = ", ".join(EMAIL_CONFIG['recipients'])
        msg['Subject'] = f"Krayin CRM Report - {datetime.now().strftime('%Y-%m-%d')}"
        
        msg.attach(MIMEText(html_content, 'html'))
        
        server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'])
        server.starttls()
        server.login(EMAIL_CONFIG['username'], EMAIL_CONFIG['password'])
        server.send_message(msg)
        server.quit()
        
        print("Email sent successfully!")
    except Exception as e:
        print(f"Failed to send email: {e}")

def main():
    # Connect to database
    conn = connect_to_db()
    if not conn:
        return
    
    try:
        # Get data
        data = get_crm_data(conn)
        
        # Generate HTML report
        html_report = generate_html_report(data)
        
        # Save report to file (optional)
        with open(f"krayin_report_{datetime.now().strftime('%Y%m%d')}.html", "w") as f:
            f.write(html_report)
        
        # Send email
        send_email(html_report)
        
    finally:
        conn.close()

if __name__ == "__main__":
    main()