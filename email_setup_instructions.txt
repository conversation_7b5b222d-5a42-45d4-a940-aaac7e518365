GMAIL SMTP SETUP INSTRUCTIONS
================================

To fix the email authentication error, you need to set up an App Password for Gmail:

1. ENABLE 2-FACTOR AUTHENTICATION:
   - Go to your Google Account settings: https://myaccount.google.com/
   - Click on "Security" in the left sidebar
   - Under "Signing in to Google", click "2-Step Verification"
   - Follow the steps to enable 2-factor authentication

2. GENERATE APP PASSWORD:
   - After enabling 2-factor authentication, go back to Security settings
   - Under "Signing in to Google", click "App passwords"
   - Select "Mail" as the app and "Windows Computer" as the device
   - Click "Generate"
   - Copy the 16-character app password (it will look like: abcd efgh ijkl mnop)

3. UPDATE THE SCRIPT:
   - Replace the password in EMAIL_CONFIG with your new app password
   - Change this line in krayin_report.py:
     'password': 'Aa@**********'
   - To:
     'password': 'your-16-character-app-password'

4. ALTERNATIVE EMAIL PROVIDERS:
   If you prefer not to use Gmail, you can use other SMTP providers:
   
   For Outlook/Hotmail:
   - smtp_server: 'smtp-mail.outlook.com'
   - smtp_port: 587
   
   For Yahoo:
   - smtp_server: 'smtp.mail.yahoo.com'
   - smtp_port: 587

CURRENT STATUS:
- Database connection: ✅ WORKING
- Data retrieval: ✅ WORKING (19 customers, 15 leads found)
- HTML report generation: ✅ WORKING
- Report saved locally: ✅ WORKING (krayin_report_20250619_113133.html)
- Email sending: ❌ NEEDS APP PASSWORD

The script is fully functional except for the email sending part.
